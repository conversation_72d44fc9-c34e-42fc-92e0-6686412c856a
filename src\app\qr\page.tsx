"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  QrCode, 
  Wifi, 
  Upload, 
  Download, 
  Printer, 
  Copy,
  CheckCircle,
  AlertCircle 
} from "lucide-react";
import { toast } from "sonner";
import QRCode from "qrcode";

interface QRState {
  wifiQR: string;
  uploadQR: string;
  isGenerating: boolean;
  error: string;
}

interface WiFiFormData {
  ssid: string;
  password: string;
  ipAddress: string;
}

export default function QRAccessPage() {
  const [qrState, setQRState] = useState<QRState>({
    wifiQR: "",
    uploadQR: "",
    isGenerating: false,
    error: "",
  });

  const [wifiForm, setWifiForm] = useState<WiFiFormData>({
    ssid: "",
    password: "",
    ipAddress: "",
  });

  const wifiCanvasRef = useRef<HTMLCanvasElement>(null);
  const uploadCanvasRef = useRef<HTMLCanvasElement>(null);

  // Generate WiFi QR Code
  const generateWiFiQR = async () => {
    if (!wifiForm.ssid.trim()) {
      toast.error("WiFi network name (SSID) is required");
      return;
    }

    setQRState(prev => ({ ...prev, isGenerating: true, error: "" }));

    try {
      // WiFi QR code format: WIFI:T:WPA;S:[SSID];P:[password];H:false;;
      const wifiString = `WIFI:T:WPA;S:${wifiForm.ssid};P:${wifiForm.password};H:false;;`;
      
      const qrDataURL = await QRCode.toDataURL(wifiString, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      setQRState(prev => ({ 
        ...prev, 
        wifiQR: qrDataURL,
        isGenerating: false 
      }));

      toast.success("WiFi QR code generated successfully!");
    } catch (error) {
      console.error("Error generating WiFi QR code:", error);
      setQRState(prev => ({ 
        ...prev, 
        error: "Failed to generate WiFi QR code",
        isGenerating: false 
      }));
      toast.error("Failed to generate WiFi QR code");
    }
  };

  // Generate Upload QR Code
  const generateUploadQR = async () => {
    setQRState(prev => ({ ...prev, isGenerating: true, error: "" }));

    try {
      // Get the current origin and create upload URL
      const uploadURL = `${window.location.origin}/upload`;
      
      const qrDataURL = await QRCode.toDataURL(uploadURL, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      setQRState(prev => ({ 
        ...prev, 
        uploadQR: qrDataURL,
        isGenerating: false 
      }));

      toast.success("Upload QR code generated successfully!");
    } catch (error) {
      console.error("Error generating upload QR code:", error);
      setQRState(prev => ({ 
        ...prev, 
        error: "Failed to generate upload QR code",
        isGenerating: false 
      }));
      toast.error("Failed to generate upload QR code");
    }
  };

  // Download QR Code
  const downloadQR = (dataURL: string, filename: string) => {
    const link = document.createElement('a');
    link.download = filename;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success(`${filename} downloaded successfully!`);
  };

  // Copy QR Code to clipboard
  const copyQRToClipboard = async (dataURL: string, type: string) => {
    try {
      const response = await fetch(dataURL);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      toast.success(`${type} QR code copied to clipboard!`);
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy QR code to clipboard");
    }
  };

  // Print QR Codes
  const printQRCodes = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const printContent = `
      <html>
        <head>
          <title>LDIS QR Access Codes</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .qr-section { margin-bottom: 40px; text-align: center; }
            .qr-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .qr-image { max-width: 300px; height: auto; }
            .wifi-info { margin-top: 10px; font-size: 14px; color: #666; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <h1 style="text-align: center; color: oklch(0.5 0.15 240);">LDIS QR Access Codes</h1>
          ${qrState.wifiQR ? `
            <div class="qr-section">
              <div class="qr-title">WiFi Connection</div>
              <img src="${qrState.wifiQR}" alt="WiFi QR Code" class="qr-image" />
              <div class="wifi-info">
                <div>Network: ${wifiForm.ssid}</div>
                ${wifiForm.ipAddress ? `<div>IP Address: ${wifiForm.ipAddress}</div>` : ''}
              </div>
            </div>
          ` : ''}
          ${qrState.uploadQR ? `
            <div class="qr-section">
              <div class="qr-title">Upload Documents</div>
              <img src="${qrState.uploadQR}" alt="Upload QR Code" class="qr-image" />
              <div class="wifi-info">Scan to access document upload page</div>
            </div>
          ` : ''}
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const handleWifiFormChange = (field: keyof WiFiFormData, value: string) => {
    setWifiForm(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 flex items-center gap-3">
            <QrCode className="h-8 w-8 text-primary" />
            QR Access
          </h1>
          <p className="text-muted-foreground">
            Generate QR codes for WiFi connection and document upload access.
            Perfect for providing easy mobile access to LDIS functionality.
          </p>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          {/* WiFi QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5 text-primary" />
                WiFi Connection QR Code
              </CardTitle>
              <CardDescription>
                Generate a QR code that automatically connects mobile devices to your WiFi network.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ssid">WiFi Network Name (SSID) *</Label>
                  <Input
                    id="ssid"
                    type="text"
                    placeholder="Enter WiFi network name"
                    value={wifiForm.ssid}
                    onChange={(e) => handleWifiFormChange('ssid', e.target.value)}
                    disabled={qrState.isGenerating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">WiFi Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter WiFi password (optional)"
                    value={wifiForm.password}
                    onChange={(e) => handleWifiFormChange('password', e.target.value)}
                    disabled={qrState.isGenerating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ipAddress">IPv4 Address (Optional)</Label>
                  <Input
                    id="ipAddress"
                    type="text"
                    placeholder="e.g., *************"
                    value={wifiForm.ipAddress}
                    onChange={(e) => handleWifiFormChange('ipAddress', e.target.value)}
                    disabled={qrState.isGenerating}
                  />
                  <p className="text-xs text-muted-foreground">
                    Optional: Specify a static IP address for network configuration
                  </p>
                </div>

                <Button 
                  onClick={generateWiFiQR}
                  disabled={qrState.isGenerating || !wifiForm.ssid.trim()}
                  className="w-full"
                >
                  <QrCode className="h-4 w-4 mr-2" />
                  Generate WiFi QR Code
                </Button>
              </div>

              {qrState.wifiQR && (
                <div className="space-y-4">
                  <Separator />
                  <div className="text-center">
                    <img 
                      src={qrState.wifiQR} 
                      alt="WiFi QR Code" 
                      className="mx-auto border rounded-lg shadow-sm"
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Scan with mobile device to connect to WiFi
                    </p>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadQR(qrState.wifiQR, 'wifi-qr-code.png')}
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyQRToClipboard(qrState.wifiQR, 'WiFi')}
                      className="flex-1"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Document Upload QR Code Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5 text-primary" />
                Document Upload QR Code
              </CardTitle>
              <CardDescription>
                Generate a QR code that links directly to the LDIS document upload page.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    This QR code will link to: <br />
                    <code className="text-xs bg-background px-2 py-1 rounded">
                      {typeof window !== 'undefined' ? `${window.location.origin}/upload` : '/upload'}
                    </code>
                  </p>
                </div>

                <Button 
                  onClick={generateUploadQR}
                  disabled={qrState.isGenerating}
                  className="w-full"
                >
                  <QrCode className="h-4 w-4 mr-2" />
                  Generate Upload QR Code
                </Button>
              </div>

              {qrState.uploadQR && (
                <div className="space-y-4">
                  <Separator />
                  <div className="text-center">
                    <img 
                      src={qrState.uploadQR} 
                      alt="Upload QR Code" 
                      className="mx-auto border rounded-lg shadow-sm"
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Scan with mobile device to access upload page
                    </p>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadQR(qrState.uploadQR, 'upload-qr-code.png')}
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyQRToClipboard(qrState.uploadQR, 'Upload')}
                      className="flex-1"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Error Alert */}
        {qrState.error && (
          <Alert variant="destructive" className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{qrState.error}</AlertDescription>
          </Alert>
        )}

        {/* Print All QR Codes */}
        {(qrState.wifiQR || qrState.uploadQR) && (
          <div className="mt-8 text-center">
            <Button
              onClick={printQRCodes}
              variant="outline"
              size="lg"
              className="gap-2"
            >
              <Printer className="h-4 w-4" />
              Print All QR Codes
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
